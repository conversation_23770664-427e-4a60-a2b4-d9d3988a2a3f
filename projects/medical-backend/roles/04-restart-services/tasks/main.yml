---
# Medical Backend Service Restart Role
# This role handles service restart operations for the medical backend infrastructure
# Supports both group-based and individual service restart operations

# Parse restart_groups variable (handle both array and string formats)
- name: Parse restart_groups variable
  ansible.builtin.set_fact:
    restart_groups_parsed: >-
      {%- set raw_groups = restart_groups | default([]) -%}
      {%- if raw_groups is string -%}
      {%- if raw_groups.startswith('[') and raw_groups.endswith(']') -%}
      {{ raw_groups | from_yaml }}{%- else -%}
      {{ [raw_groups] }}{%- endif -%}
      {%- else -%}
      {{ raw_groups }}{%- endif -%}

# Parse restart_services variable (handle both array and string formats)
- name: Parse restart_services variable
  ansible.builtin.set_fact:
    restart_services_parsed: >-
      {%- set raw_services = restart_services | default([]) -%}
      {%- if raw_services is string -%}
      {%- if raw_services.startswith('[') and raw_services.endswith(']') -%}
      {{ raw_services | from_yaml }}{%- else -%}
      {{ [raw_services] }}{%- endif -%}
      {%- else -%}
      {{ raw_services }}{%- endif -%}

- name: Validate input parameters
  ansible.builtin.fail:
    msg: |
      Invalid input parameters:
      - At least one of 'restart_groups' or 'restart_services' must be specified
      - restart_groups: {{ restart_groups_parsed }}
      - restart_services: {{ restart_services_parsed }}
  when:
    - (restart_groups_parsed | length == 0)
    - (restart_services_parsed | length == 0)

- name: Display service restart plan overview
  ansible.builtin.debug:
    msg: |
      Service Restart Plan Overview:
      =============================
      Groups to restart: {{ restart_groups_parsed | join(', ') if restart_groups_parsed | length > 0 else 'None' }}
      Individual services: {{ restart_services_parsed | join(', ') if restart_services_parsed | length > 0 else 'None' }}

      Detailed planning and execution will be handled in the main restart task.

# Build target hosts list for dynamic inventory
- name: Build target hosts list from services
  ansible.builtin.set_fact:
    target_hosts_list: []

- name: Collect target hosts from restart_groups
  ansible.builtin.set_fact:
    target_hosts_list: >-
      {{
        target_hosts_list +
        [services_info.restart.services[item].host]
      }}
  loop: >-
    {{
      restart_groups_parsed |
      map('extract', services_info.restart.groups) |
      flatten |
      unique
    }}
  when:
    - restart_groups_parsed | length > 0
    - item in services_info.restart.services

- name: Collect target hosts from restart_services
  ansible.builtin.set_fact:
    target_hosts_list: >-
      {{
        target_hosts_list +
        [services_info.restart.services[item].host]
      }}
  loop: "{{ restart_services_parsed }}"
  when:
    - restart_services_parsed | length > 0
    - item in services_info.restart.services

- name: Remove duplicate hosts and create restart_targets group
  ansible.builtin.add_host:
    name: "{{ item }}"
    groups: restart_targets
  loop: "{{ target_hosts_list | unique }}"
  when: target_hosts_list | length > 0

- name: Display target hosts for restart
  ansible.builtin.debug:
    msg: |
      Target Hosts for Service Restart:
      ================================
      Hosts: {{ target_hosts_list | unique | join(', ') if target_hosts_list | length > 0 else 'None' }}
      Total hosts: {{ target_hosts_list | unique | length }}

- name: Import service restart tasks
  ansible.builtin.import_tasks: task-restart-services.yml
  vars:
    restart_groups: "{{ restart_groups_parsed }}"
    restart_services: "{{ restart_services_parsed }}"
  when: inventory_hostname == 'localhost'
