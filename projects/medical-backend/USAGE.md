# Medical Backend Playbook Usage Guide

This document provides comprehensive usage instructions for the Medical Backend Ansible playbooks.

## Overview

The Medical Backend playbook provides four main functionalities:
1. **Script Execution** - Execute shell scripts on target hosts
2. **Monitoring Services** - Upgrade and configure Grafana monitoring stack
3. **Database Operations** - MongoDB authentication updates
4. **Service Restart** - Restart medical backend services

## 1. Script Execution

Execute shell scripts on medical backend infrastructure.

### Required Variables
- `script_name`: Name of the script file in `roles/01-common/files/`
- `target_group`: Inventory group name to execute on
- `release_tag`: Release tag for the script
- `remote_scripts_dir`: Remote directory to copy and execute scripts
- `sync_base_shells`: Whether to sync base shells to remote hosts (default: true)

### Optional Variables
- `execution_timeout`: Script timeout in seconds (default: -1, no timeout)

### Examples
```bash
# Execute Kenta backend script
ansible-playbook site.yml -t execute_shell -e "@extra-vars/kenta.backend.yml"

# Execute Bureau backend script with timeout
ansible-playbook site.yml -t execute_shell -e "@extra-vars/bureau.backend.yml" \
  --extra-vars "MEDICAL.scripts.execution_timeout=600"

# Execute custom script
ansible-playbook site.yml -t execute_shell \
  -e "target_group=medical_servers script_name=custom.sh release_tag=v1.0.0 remote_scripts_dir=/tmp/scripts"
```

## 2. Monitoring Services (Grafana Stack)

Upgrade monitoring services and update configurations.

### Required Variables
- `grafana_image_name`: grafana/grafana
- `grafana_version`: Grafana version
- `loki_image_name`: grafana/loki
- `loki_version`: Loki version
- `promtail_image_name`: grafana/promtail
- `promtail_version`: Promtail version
- `prometheus_image_name`: prom/prometheus
- `prometheus_version`: Prometheus version
- `node_exporter_image_name`: prom/node-exporter
- `node_exporter_version`: Node exporter version
- `sync_docker_compose`: Whether to sync docker-compose.yml to remote hosts (default: true)
- `relative_prometheus_config_dir`: prometheus/config (sub path after root path with docker-compose.yml)
- `relative_redis_monitor_config_dir`: service/redis/conf (sub path after root path with docker-compose.yml)
- `relative_loki_config_dir`: loki/config (sub path after root path with docker-compose.yml)

### Examples
```bash
# Upgrade all monitoring services
ansible-playbook site.yml -t medical_grafana -e "@extra-vars/monitor.yml"

# Upgrade services only
ansible-playbook site.yml -t upgrade_services -e "@extra-vars/monitor.yml"

# Update configurations only
ansible-playbook site.yml -t update_configs -e "@extra-vars/monitor.yml"
```

## 3. MongoDB Authentication Update

Update MongoDB authentication strings across all medical backend services.

### Required Variables
- `old_string`: The string to be replaced in configuration files
- `new_string`: The new string to replace with

### Affected Services
- 贤太API集群 (***********)
- 事务局 (***********)
- 贤太识别端 (************)
- Smart药局 (***********)
- GreenMedic (172.17.0.8, 172.17.0.37)
- selector环境 (***********)
- neox-inc官网 (*************)
- smart药局统计执行文件相关 (***********)

### Examples
```bash
# Update using predefined variables
ansible-playbook site.yml -t mongo_auth_update -e "@extra-vars/mongo.auth.update.yml"

# Update with inline variables
ansible-playbook site.yml -t mongo_auth_update \
  -e "old_string='yakumaru:oldpassword' new_string='yakumaru:newpassword'"

# Update MongoDB connection string
ansible-playbook site.yml -t medical_database \
  -e "old_string='***********************' new_string='***********************'"
```

## 4. Service Restart

Robust and idempotent service restart functionality supporting both group-based and individual service operations.

### Required Variables (at least one)
- `restart_groups`: List of service groups to restart
- `restart_services`: List of individual services to restart

### Available Service Groups
- **recognize** (识别端): async-merge, async-dispatcher, med-queue, async-recognize
- **qps** (QPS): selector
- **api** (API集群): php-fpm-74, nginx, med-queue
- **bureau** (事务局端): bureau-queue, med-cover-original, prescription
- **smart** (Smart药局): smart-queue

### Available Individual Services
- async-merge, async-dispatcher, med-queue, async-recognize
- selector, php-fpm-74, nginx, bureau-queue
- med-cover-original, prescription, smart-queue

### Configuration Options
Configuration can be overridden in `roles/04-restart-services/vars/main.yml`:
- `remote_logs_dir`: Log directory (default: /mnt/efs/production/devops/logs/ansible-service-restart-logs)
- `execution_timeout`: Timeout in seconds, -1 for no timeout (default: -1)
- `max_retry_attempts`: Number of retry attempts (default: 3)
- `retry_delay`: Delay between retries in seconds (default: 10)

### Examples

#### Basic Service Restart
```bash
# Restart services by group (multiple formats supported)
ansible-playbook site.yml -t restart_services -e "restart_groups=['recognize']"
ansible-playbook site.yml -t restart_services -e '{"restart_groups":["recognize","api"]}'

# Restart individual services (multiple formats supported)
ansible-playbook site.yml -t restart_services -e "restart_services=['nginx','php-fpm-74']"
ansible-playbook site.yml -t restart_services -e '{"restart_services":["med-cover-original","prescription"]}'
ansible-playbook site.yml -t restart_services -e "restart_services: [async-merge]"

# Mixed restart (groups + individual services)
ansible-playbook site.yml -t restart_services \
  -e "restart_groups=['qps'] restart_services=['nginx','bureau-queue']"
```

#### Bureau Services Examples
```bash
# Restart bureau services (med-cover-original and prescription)
ansible-playbook site.yml -t restart_services -e "restart_services=['med-cover-original','prescription']"
ansible-playbook site.yml -t restart_services -e "restart_groups=['bureau']"

# Restart single bureau service
ansible-playbook site.yml -t restart_services -e "restart_services=['med-cover-original']"
```

#### Advanced Configuration
```bash
# With custom timeout configuration
ansible-playbook site.yml -t restart_services -e "restart_groups=['api']" \
  --extra-vars "services_info.restart.defaults.execution_timeout=300"

# With verbose output for debugging
ansible-playbook site.yml -t restart_services -e "restart_services=['nginx']" -vvv
```

### Features
- Robust error handling and retry mechanism
- Pre and post-restart status verification
- Comprehensive logging and status tracking
- Idempotent operations (safe to run multiple times)
- Partial failure support (continues with other services)
- Automatic log cleanup (keeps last 20 executions)

### Log Files
Location: `/mnt/efs/production/devops/logs/ansible-service-restart-logs/`
- `service-restart-execution.log`: Main execution log
- `service-status-{service}-{timestamp}.log`: Service status logs
- `service-restart-{service}-{timestamp}.log`: Individual restart logs

## Common Tags

- `execute_shell`: Execute shell scripts
- `medical_backend`: All medical backend operations
- `medical_grafana`: Grafana monitoring operations
- `monitor`: Monitoring services
- `medical_database`: Database operations
- `mongo_auth_update`: MongoDB authentication updates
- `database`: Database operations
- `restart_services`: Service restart operations

## Inventory Groups

- `medical_servers`: Main medical backend servers (***********)
- `recognize_servers`: Recognition servers (************)
- `grafana_servers`: Monitoring servers (************)
- `homepage_servers`: Homepage servers (*************)

## Extra Variables Files

Pre-configured variable files are available in the `extra-vars/` directory:
- `kenta.backend.yml`: Kenta backend script execution
- `kenta.recognize.yml`: Kenta recognition script execution
- `bureau.backend.yml`: Bureau backend script execution
- `bureau.frontend.yml`: Bureau frontend script execution
- `smart.backend.yml`: Smart backend script execution
- `smart.frontend.yml`: Smart frontend script execution
- `homepage.app.yml`: Homepage application script execution
- `yakumaru-hp.app.yml`: Yakumaru homepage script execution
- `monitor.yml`: Monitoring services configuration
- `mongo.auth.update.yml`: MongoDB authentication update configuration
